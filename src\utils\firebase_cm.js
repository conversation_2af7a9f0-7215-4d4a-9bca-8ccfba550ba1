const firebase = require('firebase-admin');
const serviceAccountDetails = require('../../service_account_key.json');

const arrayChunks = async (arr, size) => {
  if (arr.length < size) {
    return [ arr ];
  }

  const chunks = [];

  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }

  return chunks;
};

firebase.initializeApp({
  credential: firebase.credential.cert(serviceAccountDetails)
});

/**
 * @description : Sends firebase push notification with same message to all user devices in one go.
 * @param {Array} message : Message object having list of tokens.
 */
const sendFirebasePushMulticast = async (messageBody, title, tokens, imageUrl = '') => {
  const batches = await arrayChunks(tokens, 500);

  const notificationRPCs = [];
  let message;

  batches.forEach(batch => {
    if (batch.length) {
      message = {
        tokens: batch,
        android: {
          priority: 'high',
          notification: {
            sound: 'default'
          }
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
              contentAvailable: true,
            }
          }
        },
        notification: { // Default configuration for notifications
          body: messageBody,
          title: title || 'One God'
        }
      };

      if (imageUrl) {
        message.android.notification.imageUrl = imageUrl; // Default notification
        message.apns.fcmOptions = { imageUrl: imageUrl };
        message.apns.payload.aps.mutableContent = true;
      }

      notificationRPCs.push(firebase.messaging().sendEachForMulticast(message));
    }

    console.log('FCM Message : ', JSON.stringify(message, null, 2));
  });

  const batchResponses = await Promise.all(notificationRPCs);

  console.log('FCM Batch Responses : ', JSON.stringify(batchResponses, null, 2));
};

/**
 * @description : Sends firebase push notifications to all users with their corresponding messages in one go.
 * @param {Array} messages : An array of fcm message for each token.
 */
const sendFirebasePushEach = async (messages) => {
  try {
    const multicastResponse = firebase.messaging().sendEach(messages);

    console.log('Individual Push Response : ', multicastResponse);
  } catch (error) {
    console.log('FCM Each Send Push Notification Error : ', error);
    throw new Error(error);
  }
};

module.exports = {
  sendFirebasePushMulticast,
  sendFirebasePushEach,
};
