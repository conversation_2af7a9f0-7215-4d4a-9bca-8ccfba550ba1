const { sign } = require('jsonwebtoken');
const uuid = require('uuid').v4;

const generateToken = async (payload) => {
  const uuidv4 = uuid();

  const tokenPayload = {
    _id: payload._id,
    phone: payload.phone,
    firstName: payload.firstName,
    lastName: payload.lastName,
    userType: payload.userType,
    uuid: uuidv4,
    time: new Date().getTime(),
  };
  const token = sign(tokenPayload, process.env.JWT_SECRET, { expiresIn: process.env.JWT_EXPIRY });

  return token;
};

module.exports = {
  generateToken
};