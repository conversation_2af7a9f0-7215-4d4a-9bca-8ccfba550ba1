const mongoose = require("mongoose");
const { productStatusValue } = require("../constants/dbEnums");

const productSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxLength: 100,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    price: {
      type: Number,
      required: true,
      min: 0,
    },
    images: [
      {
        type: String,
        trim: true,
      },
    ],
    discountPrice: {
      type: Number,
      min: 0,
      validate: {
        validator: function (v) {
          return !v || v < this.price;
        },
        message: "Discount price must be less than regular price",
      },
    },
    images: [
      {
        type: String,
        required: false,
        trim: true,
      },
    ],
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Category",
      required: true,
    },
    subCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "SubCategory",
      required: true,
    },
    stock: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    sku: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    // weight: {
    //   type: Number,
    //   required: true,
    //   min: 0
    // },
    // dimensions: {
    //   length: {
    //     type: Number,
    //     required: true,
    //     min: 0
    //   },
    //   width: {
    //     type: Number,
    //     required: true,
    //     min: 0
    //   },
    //   height: {
    //     type: Number,
    //     required: true,
    //     min: 0
    //   }
    // },
    // Variant configuration
    hasVariants: {
      type: Boolean,
      default: false,
    },
    variantAttributes: {
      type: [String],
      default: [],
    },
  },
  { timestamps: true }
);

// Add virtual field for variants
productSchema.virtual("variants", {
  ref: "ProductVariant",
  localField: "_id",
  foreignField: "product",
});

// Configure toJSON and toObject to include virtuals
productSchema.set("toJSON", { virtuals: true });
productSchema.set("toObject", { virtuals: true });

// Add indexes for better query performance
productSchema.index({ name: "text", description: "text" });
productSchema.index({ category: 1 });
productSchema.index({ subCategory: 1 });
productSchema.index({ temple: 1 });
productSchema.index({ status: 1 });
productSchema.index({ featured: 1 });
productSchema.index({ createdBy: 1 });

const Product = mongoose.model("Product", productSchema);

module.exports = Product;
