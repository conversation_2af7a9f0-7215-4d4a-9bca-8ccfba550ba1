const {
  apiResponse,
  errorApiResponse,
} = require("../../config/responseHandler");
const { commonConstants } = require("../../constants/common");
const { messages } = require("../../messages");
const userService = require("./service");
const { userRegistrationSchema, updateUserSchema } = require("./validation");
const { SUCCESS } = commonConstants;

const registerUser = async (req, res) => {
  try {
    const { value, error } = userRegistrationSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false,
      });
    }

    const data = await userService.addUser(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.USER_REGISTERED_SUCCESSFULLY,
      status: true,
      data,
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  registerUser,
};
