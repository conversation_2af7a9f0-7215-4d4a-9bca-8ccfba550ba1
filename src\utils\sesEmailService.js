const AWS = require('aws-sdk');

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

// Create SES service object
const ses = new AWS.SES({ apiVersion: '2010-12-01' });

/**
 * Function to send email via AWS SES
 * @param {string} senderEmail 
 * @param {string} recipientEmail 
 * @param {string} html 
 * @param {string} subject 
 * @returns {Promise}
 */
const sendEmail = async (senderEmail, recipientEmail, html, subject) => {
  const params = {
    Source: senderEmail,
    Destination: {
      ToAddresses: [ recipientEmail ],
    },
    Message: {
      Body: {
        Html: {
          Charset: 'UTF-8',
          Data: html,
        },
      },
      Subject: {
        Charset: 'UTF-8',
        Data: subject,
      },
    },
  };

  const response = await ses.sendEmail(params).promise();

  return response;
};

module.exports = {
  sendEmail,
};
