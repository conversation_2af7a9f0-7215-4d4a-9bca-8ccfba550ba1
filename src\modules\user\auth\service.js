const { findUser } = require("../../../database/queries/user.query");
const { throwBadRequestError } = require("../../../errors");
const Token = require("../../../models/Token");
const path = require("path");
const bcrypt = require("bcrypt");
const { createToken } = require("../../../database/queries/accessToken.query");
const { generateToken } = require("../../../utils/jwt");
const { userTypeValue } = require("../../../constants/dbEnums");

const loginUser = async (body) => {
  const userExists = await findUser({
    email: body.email,
  });

  if (!userExists) {
    throwBadRequestError("User not found!");
  }

  const passwordMatch = await bcrypt.compare(
    body.password,
    userExists.password
  );

  if (!passwordMatch) {
    throwBadRequestError("Invalid password!");
  }

  const token = await generateToken(userExists);

  return await createToken({
    token: token,
    userType: userTypeValue.USER,
    fcmToken: body?.fcmToken,
    userId: userExists._id,
    expiresAt: new Date().getTime() + 5 * 60 * 1000,
  });
};

const logout = async (userId, token, userType) => {
  return await Token.deleteOne({
    userId,
    token,
    userType,
  });
};

module.exports = {
  logout,
  loginUser,
};
