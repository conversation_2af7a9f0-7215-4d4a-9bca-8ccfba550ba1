/**
 * Files to create the response functions
 */
const { checkErrorCode } = require('../errors');
const { messages } = require('../messages');

/**
 *
 * @param {{
 *  res: any,
 *  code: number,
 *  message: string,
 *  status: boolean,
 *  data: any
 * }} param0
 * @returns
 */
const apiResponse = ({ res, code, message, status, data }) => {
  return res.status(code).json({
    message,
    status,
    data,
  });
};

const errorApiResponse = (res, error) => {
  // eslint-disable-next-line no-console
  console.log('error :', error);
  if (checkErrorCode(error.code) === 500) {
    const message = error.message || messages.SERVER_ERROR;

    return apiResponse({ res, code: checkErrorCode(error.code), message, status: false });
  }
  return apiResponse({ res, code: checkErrorCode(error.code), message: error.message || messages.SERVER_ERROR, status: false });
};

module.exports = {
  apiResponse,
  errorApiResponse
};
