const { sendEmail } = require('./sesEmailService');
const fs = require('fs').promises;
const handlebars = require('handlebars');
const logger = require('../errors/logger');

const renderTemplate = async (templatePath, data) => {
  try {
    // Read the HTML template file
    const templateContent = await fs.readFile(templatePath, 'utf-8');
    
    // Compile the template with Handlebars
    const template = handlebars.compile(templateContent);
    
    // Render the template with the provided data
    return template(data);
  } catch (error) {
    logger.error(`Error rendering template ${templatePath}: ${error.message}`);
    throw error;
  }
};

const sendMail = async (email, subject, templatePath, data) => {
  try {
    //* 1. Render the template with the provided data 
    const html = await renderTemplate(templatePath, data);

    //* 2. Send Email
    return sendEmail(
      process.env.AWS_SES_SENDER_EMAIL,
      email,
      html,
      subject
    );
  } catch (error) {
    logger.error(`Error sending email: ${error.message}`);
    throw error;
  }
};

module.exports = {
  sendMail
};
