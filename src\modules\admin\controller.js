const { commonConstants } = require("../../constants/common");
const { getPresignedUrl } = require("../../utils/s3Service");
const { uploadUrlSchema } = require("./validation");

const getUploadUrl = async (req, res) => {
  try {
    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message,
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, "idols");

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: "Upload URL generated successfully",
      data: uploadData,
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getUploadUrl,
};
