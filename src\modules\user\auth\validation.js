const joi = require("joi");

const loginSchema = joi.object({
  email: joi.string().email().lowercase().required(),
  password: joi.string().required(),
});

const userSignUpSchema = joi.object().keys({
  name: joi.string().min(3).max(50).trim(true).required(),
  businessName: joi.string().min(3).max(100).trim(true).required(),
  email: joi.string().trim(true).email().lowercase().required(),
  phoneNumber: joi
    .string()
    .pattern(/^\d+$/) // Ensures only digits
    .length(10) // Ensures exactly 10 characters
    .required()
    .messages({
      "string.pattern.base": "Phone number must contain only digits",
      "string.length": "Phone number must be exactly 10 digits",
      "string.empty": "Phone number is required",
    }),
  work: joi.string().min(3).trim(true).required(),
  referralCode: joi.string().trim(true).allow(null, ""),
  userType: joi.string().valid("USER", "ADMIN").default("USER"),
  profilePicture: joi.string().allow(null, ""),
  password: joi
    .string()
    .min(4)
    .max(16)
    .pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d).+$/)
    .required()
    .messages({
      "string.pattern.base":
        "Password must contain at least one uppercase letter, one lowercase letter, and one number.",
    }),
  confirmPassword: joi.string().valid(joi.ref("password")).required().messages({
    "any.only": "Confirm password must match password",
  }),
  city: joi.string().min(2).max(100).trim(true).required(),
  state: joi.string().min(2).max(100).trim(true).required(),
  pinCode: joi
    .number()
    .required()
    .messages({
      "any.required": "Pin code is required",
      "number.base": "Pin code must be a number",
    })
    .integer()
    .messages({
      "number.integer": "Pin code must be an integer",
    })
    .min(100000)
    .max(999999)
    .messages({
      "number.min": "Pin code must be a 6-digit number",
      "number.max": "Pin code must be a 6-digit number",
    }),
});

const verifyOtpSchema = joi.object({
  phone: joi
    .string()
    .pattern(/^[6-9]\d{9}$/)
    .required()
    .messages({
      "string.pattern.base":
        "Phone number must be a valid Indian number (10 digits starting with 6-9)",
      "string.empty": "Phone number is required",
    }),
  countryCode: joi
    .string()
    .pattern(/^\+91$/)
    .required()
    .messages({
      "string.pattern.base": "Country code must be +91 for Indian numbers",
      "string.empty": "Country code is required",
    }),
  otp: joi
    .string()
    .length(6)
    .pattern(/^[0-9]+$/)
    .required()
    .messages({
      "string.pattern.base": "OTP must contain only numbers",
      "string.length": "OTP must be exactly 6 digits",
      "any.required": "OTP is required",
    }),
  fcmToken: joi.string().optional().allow("", null),
});

const sendOtpSchema = joi.object({
  phone: joi
    .string()
    .pattern(/^[6-9]\d{9}$/)
    .required()
    .messages({
      "string.pattern.base":
        "Phone number must be a valid Indian number (10 digits starting with 6-9)",
      "string.empty": "Phone number is required",
    }),
  countryCode: joi
    .string()
    .pattern(/^\+91$/)
    .required()
    .messages({
      "string.pattern.base": "Country code must be +91 for Indian numbers",
      "string.empty": "Country code is required",
    }),
  // userType: joi.string().valid(1, 2, 3, 4).required(),
});

const verifyEmailSchema = joi.object({
  email: joi.string().email().lowercase().required(),
});

module.exports = {
  loginSchema,
  userSignUpSchema,
  verifyOtpSchema,
  verifyEmailSchema,
  sendOtpSchema,
};
