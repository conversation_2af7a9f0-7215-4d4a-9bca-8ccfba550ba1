const jwt = require('jsonwebtoken');
const Token = require('../models/Token');
const User = require('../models/User');

const authenticate = async (req) => {
  try {
    const authHeader = req.header('Authorization');
    const token = authHeader ? authHeader.replace('Bearer ', '') : undefined;

    if (!token) {
      return {};
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // First check if token exists in database
    const tokenDoc = await Token.findOne({
      token,
      userId: decoded.id || decoded._id,
    });

    if (!tokenDoc) {
      return {};
    }

    // Then check if token has expired
    if (tokenDoc.expiresAt < new Date()) {
      // Delete token only if it exists and has expired
      await Token.deleteOne({ _id: tokenDoc._id });
      return {};
    }

    if (decoded.userType === 'USER') {
      // Check if user is not deleted
      const user = await User.findOne({
        _id: decoded.id || decoded._id,
        deletedAt: null
      });

      if (!user) {
        return {};
      }
    }

    return {
      id: decoded.id || decoded._id,
      userType: decoded.userType,
      tokenId: tokenDoc._id
    };
  } catch (err) {
    return null;
  }
};

module.exports = {
  authenticate
};
