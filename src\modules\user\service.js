const { throwBadRequestError } = require("../../errors");
const { messages } = require("../../messages");
const { createToken } = require("../../database/queries/accessToken.query");
const { generateToken } = require("../../utils/jwt");
const { userTypeValue } = require("../../constants/dbEnums");
const bcrypt = require("bcrypt");
const { findUser, createUser } = require("../../database/queries/user.query");

/**
 * Create a new user and generate a token
 * @param {Object} userData - User data to create
 * @returns {Object} User data and token
 */
const addUser = async (userData) => {
  // Check if user already exists with the same phone number
  const existingUser = await findUser({
    phoneNumber: userData.phoneNumber,
    countryCode: userData.countryCode,
    deletedAt: null,
  });

  if (existingUser) {
    throwBadRequestError(messages.USER_ALREADY_EXISTS);
  }

  // Create the user
  userData.password = await bcrypt.hash(userData.password, 10);
  let user = await createUser(userData);

  // Generate token
  user = user.toObject();
  user.userType = userTypeValue.USER;
  const token = await generateToken(user);

  // Save token in AccessTokens collection
  await createToken({
    token: token,
    userType: userTypeValue.USER,
    fcmToken: userData?.fcmToken,
    userId: user._id,
    expiresAt: new Date().getTime() + 5 * 60 * 1000,
  });

  return {
    user: user,
    token,
  };
};

module.exports = {
  addUser,
};
