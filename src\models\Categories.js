const mongoose = require("mongoose");
const { categoryStatusValue } = require("../constants/dbEnums");

const categorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxLength: 100,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    imageKey: {
      type: String,
      required: true,
    },
    imagePath: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(categoryStatusValue),
      default: categoryStatusValue.ACTIVE,
    },
  },
  { timestamps: true }
);

// Configure toJSON and toObject to include virtuals
categorySchema.set("toJSON", { virtuals: true });
categorySchema.set("toObject", { virtuals: true });

categorySchema.index({ name: "text", description: "text" });

const Category = mongoose.model("Category", categorySchema);

module.exports = Category;
