const joi = require("joi");
const mongoose = require("mongoose");

const uploadUrlSchema = joi.object({
  extension: joi.string().valid("jpeg", "jpg", "png").required().messages({
    "any.required": "File extension is required",
    "any.only": "Invalid file extension. Only JPEG, JPG and PNG are allowed",
  }),
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error("any.invalid");
  }
  return value;
};

const validateId = joi.string().custom(validateObjectId).required().messages({
  "any.invalid": "Invalid ID format",
  "any.required": "ID is required",
});

module.exports = {
  uploadUrlSchema,
  validateId,
};
