/**
 * Simple logging utility for payment, order, and shipping-related events
 */

const logPaymentEvent = (type, data) => {
  const timestamp = new Date().toISOString();

  console.log(`[${timestamp}] [PAYMENT_${type}]`, JSON.stringify(data, null, 2));
};

const logOrderEvent = (type, data) => {
  const timestamp = new Date().toISOString();

  console.log(`[${timestamp}] [ORDER_${type}]`, JSON.stringify(data, null, 2));
};

const logShippingEvent = (type, data) => {
  const timestamp = new Date().toISOString();

  console.log(`[${timestamp}] [SHIPPING_${type}]`, JSON.stringify(data, null, 2));
};

module.exports = {
  logPaymentEvent,
  logOrderEvent,
  logShippingEvent
};
