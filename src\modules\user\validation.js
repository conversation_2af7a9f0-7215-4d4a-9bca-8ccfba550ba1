const joi = require("joi");

const userRegistrationSchema = joi.object({
  firstName: joi.string().min(3).max(50).trim(true).required().messages({
    "string.min": "First name must be at least 3 characters",
    "string.max": "First name cannot exceed 50 characters",
    "any.required": "First name is required",
  }),
  lastName: joi.string().min(3).max(50).trim(true).required().messages({
    "string.min": "Last name must be at least 3 characters",
    "string.max": "Last name cannot exceed 50 characters",
    "any.required": "Last name is required",
  }),
  phoneNumber: joi
    .string()
    .pattern(/^[6-9]\d{9}$/)
    .required()
    .messages({
      "string.pattern.base":
        "Phone number must be a valid Indian number (10 digits starting with 6-9)",
      "string.empty": "Phone number is required",
    }),
  countryCode: joi
    .string()
    .pattern(/^\+91$/)
    .required()
    .messages({
      "string.pattern.base": "Country code must be +91 for Indian numbers",
      "string.empty": "Country code is required",
    }),
  email: joi.string().email().lowercase().required().messages({
    "string.email": "Please provide a valid email address",
    "string.empty": "Email is required",
  }),
  password: joi.string().required(),
  fcmToken: joi.string().optional().allow(null, ""),
});

const searchEntitiesSchema = joi.object({
  search: joi.string().trim(true).optional().allow(null, ""),
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).default(10),
});

const updateUserSchema = joi
  .object({
    firstName: joi.string().min(3).max(50).trim(true).optional().messages({
      "string.min": "First name must be at least 3 characters",
      "string.max": "First name cannot exceed 50 characters",
      "string.empty": "First name should not be empty",
    }),
    lastName: joi.string().min(3).max(50).trim(true).optional().messages({
      "string.min": "Last name must be at least 3 characters",
      "string.max": "Last name cannot exceed 50 characters",
      "any.required": "Last name should not be empty",
    }),
    email: joi.string().email().lowercase().optional().messages({
      "string.email": "Please provide a valid email address",
      "string.empty": "Email should not be empty",
    }),
    ageGroup: joi
      .string()
      .valid("Under 18", "18-35", "36-55", "55+")
      .optional()
      .messages({
        "any.only": "Age group must be one of: Under 18, 18-35, 36-55, 55+",
      }),
    gender: joi.string().valid("Male", "Female").optional().messages({
      "any.only": "Gender must be either Male or Female",
    }),
    latitude: joi.number().optional().allow(null),
    longitude: joi.number().optional().allow(null),
    preferredLanguage: joi
      .string()
      .valid("English", "Hindi")
      .optional()
      .messages({
        "any.only": "Preferred language must be either English or Hindi",
      }),
  })
  .min(1);
module.exports = {
  userRegistrationSchema,
};
