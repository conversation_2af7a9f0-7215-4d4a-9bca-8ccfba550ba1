const cron = require("node-cron");
const { logOrderEvent } = require("../utils/logger");

/**
 * Initialize all cron jobs
 */
const initCronJobs = () => {
  // Schedule order expiration job to run every hour
  // Cron format: minute hour day-of-month month day-of-week
  // '0 * * * *' = Run at the start of every hour

  cron.schedule("0 0 * * *", async () => {});

  // You can add more scheduled jobs here

  logOrderEvent("CRON_JOBS_INITIALIZED", {
    timestamp: new Date(),
  });
};

module.exports = {
  initCronJobs,
};
